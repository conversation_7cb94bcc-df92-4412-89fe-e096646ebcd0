API,项目数量,处理状态
bike.hellobike.com/api/user.ride.bikeCheck,1,成功
ebike.hellobike.com/api/user.ev.ride.common.checkUserRide,1,成功
ebike.hellobike.com/api/user.ev.ride.common.check,1,成功
bike.hellobike.com/api/user.soro.queryPopup,1,成功
bike.hellobike.com/api/user.artemis.pay.page.order.detail,1,成功
api.hellobike.com/api/lego.bff.data,1,成功
ebike.hellobike.com/api/user.ev.ride.normal.predictCanPark,1,成功
bike.hellobike.com/api/user.ride.pre.ride,1,成功
bike.hellobike.com/api/user.soro.getPopup,1,成功
bike.hellobike.com/api/tw.bike.ride.check,2,成功
bike.hellobike.com/api/user.artemis.tw.finish.detail,1,成功
bike.hellobike.com/api/tw.bike.index.near.bikes,30,成功
bos.hellobike.com/maintApi/map.bikes.aggregation,3,成功
ebike.hellobike.com/api/user.ev.ride.normal.canRide,1,成功
bike.hellobike.com/api/bike.main.properties.info,1,成功
bos.hellobike.com/maintApi/maint.bike.findBikeNoByShowNo,2,成功
bos.hellobike.com/maintApi/maint.bos.scanBikeReport,1,成功
bos.hellobike.com/maintApi/maint.frame.preScanCheck,1,成功
ride-platform.hellobike.com/api/user.brand.ride.checkUserRide,1,成功
bos.hellobike.com/maintApi/flame.instance.map.query,4,成功
ride-platform.hellobike.com/api/user.brand.ride.ridingPage,1,成功
bos.hellobike.com/maintApi/flame.instance.info.detail.my,9,成功
bos.hellobike.com/maintApi/flame.instance.info.detail,13,成功
bos.hellobike.com/maintApi/map.bike.mapDetail,1,成功
api.hellobike.com/api/xiaoduo.chat.hotQuestion.ModelWithBestOrder,1,成功
ride-platform.hellobike.com/api/user.brand.ride.unlockPage,2,成功
css-workbench-api.hellobike.com/api/css.workbench.executeApi,1,成功
ride-platform.hellobike.com/api/user.brand.ride.mainPage.check,1,成功
bos.hellobike.com/maintApi/flame.instance.park.detail,1,成功
hxm.hbmar.cn/api/map.bikes.aggregation,10,成功
ride-platform.hellobike.com/api/user.brand.ride.nearbyBikes,30,成功
bos.hellobike.com/maintApi/bike.operation.openLock,1,成功
bos.hellobike.com/maintApi/bos.bike.info,1,成功
bos.hellobike.com/maintApi/map.bos.openLock,1,成功
api.hellobike.com/api/xiaoduo.chat.sayHello,1,成功
ride-platform.hellobike.com/api/user.brand.ride.scanCode,1,成功
bos.hellobike.com/maintApi/power.evBosData.checkStateV2,1,成功
bos.hellobike.com/maintApi/bike.alias.translationAliasCache,2,成功
bos.hellobike.com/maintApi/bike.operation.closeLock,1,成功
bike.hellobike.com/api/tw.red.packet.task.list,107,成功
bos.hellobike.com/maintApi/map.bos.closeLock,1,成功
bos.hellobike.com/maintApi/maint.operation.getBellModelCommand,1,成功
bike.hellobike.com/api/tw.bike.reserve.check,1,成功
bike.hellobike.com/api/tw.bike.vehicle.detail,1,成功
bos.hellobike.com/maintApi/bos.schedule.vehicle.source.bikeMapAggr,21,成功
api.hellobike.com/api/xiaoduo.chat.history,1,成功
ride-platform.hellobike.com/api/tw.tiny.brand.payPage,2,成功
api.hellobike.com/api/xiaoduo.business.order.switch,18,成功
ride-platform.hellobike.com/api/tw.tiny.brand.orderFinish,1,成功
api.hellobike.com/api/cs.self.service.task,1,成功
bos.hellobike.com/maintApi/maint.bike.findShowNoByBikeNo,1,成功
bos.hellobike.com/maintApi/bos.bikeHealthCheck.getBikeHealthDetail,1,成功
ride-platform.hellobike.com/api/user.brand.ride.createPrePay,2,成功
hxm.hbmar.cn/api/maint.bike.findBikeNoByShowNo,2,成功
bos.hellobike.com/maintApi/bos.schedule.closeDetail,6,成功
hxm.hbmar.cn/api/maint.bos.scanBikeReport,1,成功
bos.hellobike.com/maintApi/bos.common.headerInfo,1,成功
ebike.hellobike.com/api/tw.bike.ride.picAppeal.parkCheck,1,成功
hxm.hbmar.cn/api/map.bike.mapDetail,1,成功
bos.hellobike.com/maintApi/qulity.warehouse.queryPreCheckInfo,1,成功
bos.hellobike.com/maintApi/workcenter.platform.getWorkOrderDetail,1,成功
ebike.hellobike.com/api/tw.bike.ride.scanCode.parkCheck,1,成功
bos.hellobike.com/maintApi/map.nearby.bikes,2,成功
ebike.hellobike.com/api/user.scenic.ride.check,1,成功
bos.hellobike.com/maintApi/bos.mark.newMarkScanCheck,1,成功
hxm.hbmar.cn/api/bos.schedule.inScheduleList,12,成功
bos.hellobike.com/maintApi/quality.project.queryFormPage,1,成功
bos.hellobike.com/maintApi/bos.bikeHealthCheck.bikeHealthCheck,2,成功
bos.hellobike.com/maintApi/qulity.warehouse.maintenanceScanCheck,1,成功
bos.hellobike.com/maintApi/salary.platform.querySalaryClassifySummary,1,成功
bos.hellobike.com/maintApi/maint.bike.getDetectionRecordList,20,成功
bos.hellobike.com/maintApi/bos.mark.getMarkPointDetailInfo,2,成功
bos.hellobike.com/maintApi/bos.schedule.vehicle.source.bikeMapCard,1,成功
bos.hellobike.com/maintApi/lock.intelligent.check.openLock,1,成功
ebike.hellobike.com/api/user.scenic.ride.order.platform,1,成功
bos.hellobike.com/maintApi/bos.mark.markedBikeHandle,2,成功
css-workbench-api.hellobike.com/api/css.workflow.findGuideChat4WorkBench,1,成功
bos.hellobike.com/maintApi/bos.follow.follow,1,成功
bos.hellobike.com/maintApi/maint.bike.getBikeBaseInfoByIntelligent,1,成功
bos.hellobike.com/maintApi/flame.instance.list.detail,2,成功
api.hellobike.com/api/user.ride.main.bikeInfo,1,成功
bos.hellobike.com/maintApi/maint.evbike.closeHelmetLock,1,成功
css-workbench-api.hellobike.com/api/css.bike.order.queryByOrderForWork,9,成功
bos.hellobike.com/maintApi/maint.garage.inGarageCheck,1,成功
bos.hellobike.com/maintApi/maint.bike.intelligentDetectionScan,1,成功
hxm.hbmar.cn/api/bos.bike.info,1,成功
css-workbench-api.hellobike.com/api/css.pob.evOrder.getOrderForWork,9,成功
bos.hellobike.com/maintApi/maint.evbike.openHelmetLock,1,成功
bos.hellobike.com/maintApi/lock.intelligent.check.closeLock,1,成功
ebike.hellobike.com/api/ev.config.ride.getBikeListByLngAndlat,10,成功
bos.hellobike.com/maintApi/maint.garage.outGarageCheck,1,成功
bos.hellobike.com/maintApi/refurbish.operation.getProjectDetailInfo,1,成功
bos.hellobike.com/maintApi/bos.warehouse.finishBikeMaintenanceRecord,1,成功
bos.hellobike.com/maintApi/bos.disassemble.scanFrameOrBikeNo,1,成功
bos.hellobike.com/maintApi/bos.bikeHealthCheck.getLatestBikeHealthCheck,1,成功
bos.hellobike.com/maintApi/maint.frame.getBosFrameInfo,1,成功
hxm.hbmar.cn/api/bos.schedule.closeDetail,1,成功
bos.hellobike.com/maintApi/bos.mark.removeMarkedBike,2,成功
ebike.hellobike.com/api/user.scenic.home.getNearbyBikeList,50,成功
bike.hellobike.com/api/ride.license.user.record,2,成功
bos.hellobike.com/maintApi/bos.disassemble.scanAssets,1,成功
ride-platform.hellobike.com/api/user.brand.ride.bikeDetail,1,成功
ebike.hellobike.com/api/user.scenic.ride.getLockStatus,1,成功
ebike.hellobike.com/api/user.scenic.ride.prepay.canRide,1,成功
bos.hellobike.com/maintApi/bosAsset.queryLicensePlateNoInfoAPP,1,成功
api.hellobike.com/api/homepage.business.vehicle.list,2,成功
ride-platform.hellobike.com/api/tw.rental.storeDetail,19,成功
bos.hellobike.com/maintApi/bosAsset.scanCheck,1,成功
government-zhixun-api.hellobike.com/api/police.powerbike.checkOrder,1,成功
api.hellobike.com/api/cs.self.bike.current.location,1,成功
ebike.hellobike.com/api/user.scenic.ride.scBikePortionConfig,1,成功
bos.hellobike.com/maintApi/maint.mark.getNewMarkInfoByMarkPointGuidAndBikeNo,2,成功
bos.hellobike.com/maintApi/maint.user.getBikeMaintainList,1,成功
ebike.hellobike.com/api/user.ev.ride.common.homePage,1,成功
bos.hellobike.com/maintApi/bos.sales.getSalesSubjectList,2,成功
bos.hellobike.com/maintApi/bos.bikeHealthCheck.getBikeHealthCheckInfo,1,成功
scenic-platform.hellobike.com/api/scenic.bike.union,9,成功
bos.hellobike.com/maintApi/power.bike.getBikeMaintainList,1,成功
bos.hellobike.com/maintApi/sw.data.elec.lackTopBike,1,成功
bos.hellobike.com/maintApi/helmet.detect.recordList,1,成功
bos.hellobike.com/maintApi/bos.follow.cancelFollow,1,成功
hxs.hbmar.cn/saas/reverse/rideOrderDetail/saas/reverse/rideOrderDetail,1,成功
bos.hellobike.com/maintApi/bos.follow.getFollowRecord,1,成功
bos.hellobike.com/maintApi/switch.power.evBosData.checkStateV2,1,成功
bos.hellobike.com/maintApi/map.bike.getBikeInfos,1,成功
api.hellobike.com/api/cs.self.ticket.querySelfAutoHandleTicketOrderList,9,成功
css-workbench-api.hellobike.com/api/css.hitch.guide.rent.getOrderList,1,成功
ebike.hellobike.com/api/user.ev.ride.order.platform,1,成功
bos.hellobike.com/maintApi/maint.evbike.biz,1,成功
bos.hellobike.com/maintApi/scan.record.check,1,成功
bos.hellobike.com/maintApi/bos.schedule.vehicle.source.bikeMapList,2,成功
bos.hellobike.com/maintApi/maint.simple.bikeInfo,1,成功
ebike.hellobike.com/api/user.scenic.ride.checkUserRide,1,成功
bos.hellobike.com/maintApi/bike.alias.translationBikeNos,8,成功
twblueadmin-sg.hellobike.cn/blue/order/getUserOrderList,6,成功
hds-service.hellobike.cn/api/bike/getBikeKey,25,成功
bos.hellobike.com/maintApi/maint.powerBike.updateLocation,1,成功
api.hellobike.com/api/cs.self.bike.current.info,1,成功
ebike.hellobike.com/api/user.scenic.ride.rideEndDetail,1,成功
hxm.hbmar.cn/api/map.nearby.bikes,3,成功
hxs.hbmar.cn/saas/reverse/orderDetailList/saas/reverse/orderDetailList,10,成功
hds-service.hellobike.cn/api/bikeSimulator/getBikeInfoDetail,1,成功
api-iot.hellobike.com/api/device-manager/queryMultiLog/api/device-manager/queryMultiLog,1,成功
bos.hellobike.com/maintApi/parts.assetsNo.create,1,成功
bos.hellobike.com/maintApi/maint.follow.getBikeList,9,成功
bos.hellobike.com/maintApi/sw.data.area.elecDetail,17,成功
scenic-platform.hellobike.com/api/scenic.bike.manage.getDetailInfo,1,成功
bssserver.hellobike.cn/api/batchUpdate/list,8,成功
bos.hellobike.com/maintApi/salary.platform.queryTimeSalaryDetail,5,成功
bos.hellobike.com/maintApi/bos.schedule.dayDeatil,15,成功
bos.hellobike.com/maintApi/maint.bike.getDetectionCheckResult,1,成功
bos.hellobike.com/maintApi/quality.project.queryDeviceActivateResult,1,成功
government-zhixun-api.hellobike.com/api/police.powerbike.getBikeLocMap,3,成功
bos.hellobike.com/maintApi/bos.maintenance.changeAliasQrSubmit,1,成功
api-iot.hellobike.com/api/device-manager/queryDeviceLog/api/device-manager/queryDeviceLog,1,成功
bssserver.hellobike.cn/api/lockUpgrade/list,10,成功
a.hellobike.com/evehicle/api/rent.user.verification.send.coupon.and.bike.detail,1,成功
hxm.hbmar.cn/api/workcenter.platform.getWorkOrderDetail,1,成功
bos.hellobike.com/maintApi/bike.operation.openBatteryLock,1,成功
hxs.hbmar.cn/saas/center/queryRideOrderList/saas/center/queryRideOrderList,6,成功
bos.hellobike.com/maintApi/maint.lock.scanLockCheck,2,成功
bike.hellobike.com/api/user.ride.history,4,成功
hxs.hbmar.cn/tw/rental/admin/conditionQuery/tw/rental/admin/conditionQuery,4,成功
bssserver.hellobike.cn/api/bikeupgrade/detail,1,成功
bos.hellobike.com/maintApi/patrol.record.getTaskRecordDetail,2,成功
bos.hellobike.com/maintApi/maint.bike.getBikeIntelligentDetectionDetail,1,成功
hxm.hbmar.cn/api/bos.intervene.action,1,成功
supplychainapp.hellobike.com/delivery/queryBikeOperationCoreInfo/delivery/queryBikeOperationCoreInfo,130,成功
hxm.hbmar.cn/api/flame.saas.batch.lock.result,10,成功
scenic-platform.hellobike.com/api/scenic.trade.order.list,7,成功
bike.hellobike.com/api/tw.bike.fault.config.query,1,成功
bos.hellobike.com/maintApi/flame.instance.order.salary,27,成功
hxm.hbmar.cn/api/maint.simple.bikeInfo,1,成功
bos.hellobike.com/maintApi/maint.lock.submitReplaceLockInfo,1,成功
hxm.hbmar.cn/api/maint.frame.getBosFrameInfo,1,成功
ohox-server.hellobike.cn/vehicleInfo/getBikeInfoByBikeNo,1,成功
ohox-server.hellobike.cn/vehicleInfo/getBikeRideList,1,成功
bssserver.hellobike.cn/remoteControl/list,5,成功
api-iot.hellobike.com/api/job/list/api/job/list,1,成功
hxs.hbmar.cn/tw/rental/site/detail/tw/rental/site/detail,11,成功
ohox-server.hellobike.cn/ebike/getEbikeOrderList,3,成功
bos.hellobike.com/maintApi/flame.instance.depot.pickup.detail.list,6,成功
bos.hellobike.com/maintApi/switch.power.bike.addLostBikeTag,1,成功
ebike.hellobike.com/api/user.ev.ride.common.rideEndPage,1,成功
scenic-platform.hellobike.com/api/scenic.trade.order.detail,1,成功
bos.hellobike.com/maintApi/workcenter.appeal.getWorkOrderAppealInfo,99,成功
hxm.hbmar.cn/api/qulity.warehouse.maintenanceScanCheck,1,成功
ohox-server.hellobike.cn/vehicleInfo/getEBikeInfoByBikeNo,1,成功
ohox-server.hellobike.cn/vehicleInfo/getEBikeOrderListByBikeNo,1,成功
twblueadmin-sg.hellobike.cn/blueGov/bike/record/queryBikeRecord,1,成功
bos.hellobike.com/maintApi/bos.schedule.task.getApprovalOrderDetail,25,成功
bos.hellobike.com/maintApi/maint.bike.getIntelligentDetectionRecord,1,成功
ohox-server.hellobike.cn/bike/getOrderList,1,成功
voc.hellobike.cn/voc/voice/feedback/queryVoiceFeedBackListForUser,1,成功
hxs.hbmar.cn/saas/center/rideOrderDetail/saas/center/rideOrderDetail,2,成功
api.hellobike.com/api/get.order.details.online,1,成功
bos.hellobike.com/maintApi/maint.bos.powerbike.pile.lockBike,1,成功
hxm.hbmar.cn/api/qulity.warehouse.queryPreCheckInfo,2,成功
anls.hellobike.cn/position/diagnose/indicator/list,1,成功
hxs.hbmar.cn/subBranch/fault/faultDetails/subBranch/fault/faultDetails,7,成功
bos.hellobike.com/maintApi/scrap.dismantle.getBikeScrapDetailList,5,成功
bos.hellobike.com/maintApi/workcenter.appeal.getAppealInfo,14,成功
hxm.hbmar.cn/api/power.bike.getBikeMaintainList,1,成功
api.hellobike.com/api/user.ev.ride.detail,1,成功
bos.hellobike.com/maintApi/maint.bos.powerbike.pile.unlock,1,成功
hxm.hbmar.cn/api/bos.schedule.scheduleDetail,60,成功
bos.hellobike.com/maintApi/maint.scrap.getScrapConfirmInfo,1,成功
bos.hellobike.com/maintApi/scrap.dismantle.scanScrapBikeNew,1,成功
hxm.hbmar.cn/api/bos.lock.closeHelmetLock,1,成功
hxm.hbmar.cn/api/bos.lock.openHelmetLock,1,成功
bike.hellobike.com/api/ride.license.score.details,1,成功
bos.hellobike.com/maintApi/agent.data.getEstimateDepreciationFee,4,成功
bos.hellobike.com/maintApi/bike.bos.getTenantInfo,1,成功
bssserver.hellobike.cn/api/log/list,1,成功
hxs.hbmar.cn/workcenter/queryWorkOrderSmallBrandsList/workcenter/queryWorkOrderSmallBrandsList,20,成功
ohox-server.hellobike.cn/bike/getOrderDetail,1,成功
scenic-platform.hellobike.com/api/scenic.bike.batchOpenSaasBikeLock,1,成功
a.hellobike.com/evehicle/api/rent.order.findBike,1,成功
agent.hellobike.com/agentAdmin/pageQueryWorkCenterOrderList/agentAdmin/pageQueryWorkCenterOrderList,10,成功
bos.hellobike.com/maintApi/battery.miss.reportDetail,1,成功
bos.hellobike.com/maintApi/scrap.dismantle.getBikeScrapDetail,2,成功
ride-platform.hellobike.com/api/tw.rental.swap.battery.processInfo,2,成功
bos.hellobike.com/maintApi/battery.miss.reportList,3,成功
bos.hellobike.com/maintApi/maint.bikeUnload.getBikeUnloadRecordList,30,成功
bos.hellobike.com/maintApi/quality.project.deviceActivate,1,成功
bos.hellobike.com/maintApi/quality.project.queryBatchRecordDetail,30,成功
evbikeadmin.hellobike.com/license/getBindingInfo/license/getBindingInfo,20,成功
evbikeadmin.hellobike.com/taskManage/getFindBikeTask/taskManage/getFindBikeTask,1,成功
twblueadmin-sg.hellobike.cn/blue/order/queryBikeOrderByBikeNo,1,成功
agent.hellobike.com/agent/zheJiu/exclude/list/agent/zheJiu/exclude/list,20,成功
api.hellobike.com/api/homepage.business.vehicle.detail,1,成功
bos.hellobike.com/maintApi/bike.find.query,3,成功
bos.hellobike.com/maintApi/helmet.detect.recordDetail,1,成功
bos.hellobike.com/maintApi/maint.operation.shortCommandResultParse,1,成功
bos.hellobike.com/maintApi/switch.maint.evBosData.simpleInfo,1,成功
bos.hellobike.com/maintApi/warehouse.gather.queryGatherDetail,1,成功
ebike.hellobike.com/api/user.ride.history,10,成功
evbikeadmin.hellobike.com/bike/getBikeScheduleDetailList/bike/getBikeScheduleDetailList,49,成功
evbikeadmin.hellobike.com/taskManage/detail/taskManage/detail,1,成功
government-zhixun-api.hellobike.com/api/police.powerbike.getBikeLatestLoc,1,成功
hxm.hbmar.cn/api/bos.schedule.dayDeatil,10,成功
scenic-platform.hellobike.com/api/scenic.bike.page.list,20,成功
supplychainerp.hellobike.cn/price/valuation/queryValuationOrderDetail,10,成功
agent.hellobike.com/salary/platform/bill/querySalaryPlatformPiecesBillDetail/salary/platform/bill/querySalaryPlatformPiecesBillDetail,10,成功
api-iot-sg.hellobike.cn/api/device-manager/queryMultiLog,1,成功
api.hellobike.com/api/flame.instance.crowdsourcing.mytask.detail,1,成功
api.hellobike.com/api/user.ride.history,8,成功
api.ttbike.com.cn/api/user.ev.ride.detail,1,成功
bike.hellobike.cn/api/tw.bike.ride.check,1,成功
bike.hellobike.cn/api/user.ride.bikeCheck,1,成功
bos.hellobike.com/maintApi/bos.bike.submitNpsInfo,1,成功
bos.hellobike.com/maintApi/bos.center.getSortAggregateInfo,106,成功
bos.hellobike.com/maintApi/bos.rideDetect.preInfo,1,成功
bos.hellobike.com/maintApi/bosAssets.imageDetectForLicense,1,成功
bos.hellobike.com/maintApi/maint.asset.getAssetBindingInfoByEntityNo,2,成功
bos.hellobike.com/maintApi/maint.asset.replenishAssetCheck,1,成功
bos.hellobike.com/maintApi/maint.asset.replenishAssetSubmit,1,成功
bos.hellobike.com/maintApi/maint.bike.changeEvBikeFollowStatus,1,成功
bos.hellobike.com/maintApi/maint.bikeQC.checkQC,1,成功
bos.hellobike.com/maintApi/maint.fixedcheck.getDailyCheckResultList,2,成功
bos.hellobike.com/maintApi/maint.garage.historyDetailList,22,成功
bos.hellobike.com/maintApi/maint.lock.queryLockUpgradeInfoResult,1,成功
bos.hellobike.com/maintApi/patrol.share.getShareRecord,73,成功
bos.hellobike.com/maintApi/power.charge.checkScan,1,成功
bos.hellobike.com/maintApi/power.operation.closeLock,1,成功
bos.hellobike.com/maintApi/salary.platform.queryPieceSalaryDetail,12,成功
bos.hellobike.com/maintApi/sw.task.cause,2,成功
bos.hellobike.com/maintApi/switch.power.evBosData.getBatteryRecordV2,18,成功
bos.hellobike.com/maintApi/urban.govern.BikeInfoDetail,1,成功
bssserver.hellobike.cn/api/bike/mcu/list,1,成功
bssserver.hellobike.cn/api/paramUpgrade/selectPage,11,成功
bssserver.hellobike.cn/riding/info/queryList,10,成功
ebike.hellobike.com/api/user.ev.ride.common.evBikePortionConfig,1,成功
ebike.hellobike.com/api/user.scenic.ride.rideEndUnpaidPage,1,成功
evbikeadmin.hellobike.com/bike/getBikeCheckReviewList/bike/getBikeCheckReviewList,2,成功
evbikeadmin.hellobike.com/bos/getBikeSimpleEntityInfo/bos/getBikeSimpleEntityInfo,2,成功
evbikeadmin.hellobike.com/bos/getChangeStatusRecordInfos/bos/getChangeStatusRecordInfos,2,成功
evbikeadmin.hellobike.com/bos/getWorkOrderInfos/bos/getWorkOrderInfos,1,成功
evbikeadmin.hellobike.com/bosEvent/getBosEventSimpleInfo/bosEvent/getBosEventSimpleInfo,1,成功
evbikeadmin.hellobike.com/quality/project/recordQuery/quality/project/recordQuery,10,成功
evbikeadmin.hellobike.com/scrap/dismantle/getBikeScrapDetail/scrap/dismantle/getBikeScrapDetail,1,成功
evbikeadmin.hellobike.com/turnover/getBasicInfo/turnover/getBasicInfo,1,成功
evbikeadmin.hellobike.com/warehouse/infoManage/queryMaintInfo/warehouse/infoManage/queryMaintInfo,1,成功
government-zhixun-api.hellobike.com/api/police.powerbike.getUseEBikeDetails,1,成功
govx-api.hellobike.cn/selection/commitSelection,10,成功
govx-api.hellobike.cn/selection/commitSelection/selection/commitSelection,10,成功
govx-api.hellobike.cn/selection/queryStayBikeFile,4,成功
govx-api.hellobike.cn/selection/queryStayBikeFile/selection/queryStayBikeFile,10,成功
hxm.hbmar.cn/api/maint.bos.queryEventRecord,20,成功
hxs.hbmar.cn/subBranch/fault/markFaultLabel/subBranch/fault/markFaultLabel,1,成功
hxs.hbmar.cn/workcenter/platform/getWorkOrderDetail/workcenter/platform/getWorkOrderDetail,1,成功
radmin.hellobike.com/rent/merchant/lease/queryReturnAndRefundInfo/rent/merchant/lease/queryReturnAndRefundInfo,1,成功
scenic-platform.hellobike.com/api/scenic.bike.manage.getBikePosition,1,成功
scenic-platform.hellobike.com/api/scenic.bike.manage.getOperateLogInfo,1,成功
scenic-platform.hellobike.com/api/scenic.bike.queryBatchOperateLogDetail,4,成功
scenic-platform.hellobike.com/api/scenic.merchant.ticket.list,20,成功
supplychainadmin.hellobike.cn/sim/platform/base/querySimAssetRelevanceInfo,1,成功
supplychainadmin.hellobike.cn/sim/platform/base/querySimAssetRelevanceInfo/sim/platform/base/querySimAssetRelevanceInfo,1,成功
supplychainadmin.hellobike.cn/sim/platform/config/usageWarningPageList,16,成功
supplychainadmin.hellobike.cn/sim/platform/report/pageQueryImeiWarningReport,1,成功
supplychainerp.hellobike.cn/pfs/product/page,10,成功
swmerchantplatform.hellobike.com/api/switch.station.merchant.smartCore.shop.condtion.page,1,成功
twblueadmin-sg.hellobike.cn/blue/order/querySubOrder,2,成功
