#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IP分析报告生成器
分析带有地理位置和来源信息的IP数据
"""

import csv
from collections import defaultdict, Counter

def generate_report(csv_file='ip_with_source.csv'):
    """生成IP分析报告"""
    
    # 数据统计
    total_requests = 0
    company_requests = 0
    external_requests = 0
    
    country_stats = defaultdict(int)
    city_stats = defaultdict(int)
    isp_stats = defaultdict(int)
    
    company_ips = []
    top_external_ips = []
    
    print("IP访问分析报告")
    print("=" * 60)
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                ip = row['ip']
                requests = int(row.get('请求次数', row.get('数量', 0)))
                country = row['国家']
                city = row['城市']
                isp = row['ISP']
                source = row['IP来源']
                location = row['完整位置']
                
                total_requests += requests
                
                # 按来源统计
                if source == '公司出口':
                    company_requests += requests
                    company_ips.append((ip, requests, location))
                else:
                    external_requests += requests
                    top_external_ips.append((ip, requests, location))
                
                # 地理位置统计
                if country != '未知':
                    country_stats[country] += requests
                if city != '未知':
                    city_stats[city] += requests
                if isp != '未知':
                    isp_stats[isp] += requests
        
        # 排序
        company_ips.sort(key=lambda x: x[1], reverse=True)
        top_external_ips.sort(key=lambda x: x[1], reverse=True)
        
        # 生成报告
        print(f"\n📊 总体统计")
        print("-" * 40)
        print(f"总请求次数: {total_requests:,}")
        print(f"公司出口请求: {company_requests:,} ({company_requests/total_requests*100:.1f}%)")
        print(f"外部访问请求: {external_requests:,} ({external_requests/total_requests*100:.1f}%)")
        
        print(f"\n🏢 公司出口IP详情 (共{len(company_ips)}个)")
        print("-" * 40)
        for ip, requests, location in company_ips:
            print(f"{ip:<15} {requests:>8,}次 {location}")
        
        print(f"\n🌐 外部访问TOP 10")
        print("-" * 40)
        for i, (ip, requests, location) in enumerate(top_external_ips[:10], 1):
            print(f"{i:2d}. {ip:<15} {requests:>8,}次 {location}")
        
        print(f"\n🗺️  国家/地区访问统计 TOP 5")
        print("-" * 40)
        for i, (country, requests) in enumerate(Counter(country_stats).most_common(5), 1):
            print(f"{i}. {country:<10} {requests:>8,}次 ({requests/total_requests*100:.1f}%)")
        
        print(f"\n🏙️  城市访问统计 TOP 5")
        print("-" * 40)
        for i, (city, requests) in enumerate(Counter(city_stats).most_common(5), 1):
            print(f"{i}. {city:<10} {requests:>8,}次 ({requests/total_requests*100:.1f}%)")
        
        print(f"\n🌐 ISP统计 TOP 5")
        print("-" * 40)
        for i, (isp, requests) in enumerate(Counter(isp_stats).most_common(5), 1):
            isp_short = isp[:30] + "..." if len(isp) > 30 else isp
            print(f"{i}. {isp_short:<33} {requests:>8,}次")
        
        print(f"\n💡 关键发现")
        print("-" * 40)
        print(f"• 公司出口IP占总请求的 {company_requests/total_requests*100:.1f}%")
        print(f"• 最活跃的公司出口IP: {company_ips[0][0]} ({company_ips[0][1]:,}次)")
        print(f"• 最活跃的外部IP: {top_external_ips[0][0]} ({top_external_ips[0][1]:,}次)")
        print(f"• 主要访问来源国家: {Counter(country_stats).most_common(1)[0][0]}")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file}")
    except Exception as e:
        print(f"生成报告时发生错误: {str(e)}")

if __name__ == "__main__":
    generate_report()
