#!/usr/bin/env python3
"""
专门测试特定API的脚本
"""

import requests
import urllib.parse
import json

def test_specific_api():
    # 测试的API
    api = "ebike.hellobike.com/api/user.ev.ride.common.check"
    
    # 通用的请求头
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'cookie': 'sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22ba9b7a4e5e48ce4b2dcb8434ccfd0254%22%2C%22first_id%22%3A%2218457014584980-00e28086d9054478-18525635-3686400-18457014585a45%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg0NTcwMTQ1ODQ5ODAtMDBlMjgwODZkOTA1NDQ3OC0xODUyNTYzNS0zNjg2NDAwLTE4NDU3MDE0NTg1YTQ1IiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiYmE5YjdhNGU1ZTQ4Y2U0YjJkY2I4NDM0Y2NmZDAyNTQifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22ba9b7a4e5e48ce4b2dcb8434ccfd0254%22%7D%2C%22%24device_id%22%3A%2218457014584980-00e28086d9054478-18525635-3686400-18457014585a45%22%7D; _hjSessionUser_2777326=eyJpZCI6Ijc2Y2FjZDhmLTU2MjEtNWQ2Ny05OTg5LWExOTBhMjgwNmE2NyIsImNyZWF0ZWQiOjE2NzM4NzI2MTYwMTgsImV4aXN0aW5nIjp0cnVlfQ==; ubt_ssid=7sybshb0xsedsfldqihwwv4uvhlkj1o2_2024-07-11; sso_token=bearer_635cb714-e62b-471f-86bd-f476aeeda3c9; btdp-sess=709f4384-a57d-456d-9778-1ddfb7dc42d3; btdp-sess.sig=GF-k4glw5so_LbreA2obnL-l38g',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    }
    
    print("=" * 80)
    print(f"测试API: {api}")
    print("=" * 80)
    
    # 步骤1: 调用接口1
    encoded_api = urllib.parse.quote(api, safe='')
    api1_url = f"https://yazx.hellobike.cn/api/api/assets/sens-sample/info?api={encoded_api}&method=POST"
    
    print(f"\n步骤1: 调用接口1")
    print(f"URL: {api1_url}")
    
    response1 = requests.get(api1_url, headers=headers, timeout=30)
    if response1.status_code != 200:
        print(f"接口1调用失败，状态码: {response1.status_code}")
        return
    
    data1 = response1.json()
    print(f"接口1响应状态: 成功")
    
    # 步骤2: 查找所有可用的reqid
    print(f"\n步骤2: 查找所有'sens_type': '单车编号信息'的reqid")
    
    reqids = []
    if 'data' in data1 and 'rspData' in data1['data']:
        for rsp_item in data1['data']['rspData']:
            if rsp_item.get('sens_type') == '单车编号信息':
                for data_item in rsp_item['data']:
                    if 'data' in data_item:
                        for item in data_item['data']:
                            if 'reqid' in item and 'node_tag' in item:
                                reqids.append((item['reqid'], item['node_tag']))
    
    print(f"找到 {len(reqids)} 个reqid:")
    for i, (reqid, node_tag) in enumerate(reqids):
        print(f"  {i+1}. reqid: {reqid}, node_tag: {node_tag}")
    
    if not reqids:
        print("未找到任何reqid")
        return
    
    # 步骤3: 测试所有reqid
    print(f"\n步骤3: 测试所有reqid")
    
    for i, (reqid, node_tag) in enumerate(reqids):
        print(f"\n--- 测试 reqid {i+1}/{len(reqids)} ---")
        api2_url = f"https://yazx.hellobike.cn/api/api/assets/sens-sample/detail?reqid={reqid}&node_tag={node_tag}&decode=true"
        print(f"URL: {api2_url}")
        
        response2 = requests.get(api2_url, headers=headers, timeout=30)
        if response2.status_code != 200:
            print(f"接口2调用失败，状态码: {response2.status_code}")
            continue
        
        data2 = response2.json()
        print(f"接口2响应状态: 成功")
        
        # 检查响应
        if 'errno' in data2 and data2['errno'] != 0:
            print(f"接口2返回错误: errno={data2['errno']}, message={data2.get('message', 'N/A')}")
        elif 'data' in data2 and 'sens_details' in data2['data']:
            sens_details = data2['data']['sens_details']
            item_count = len(sens_details)
            print(f"✅ 成功! sens_details内容: {sens_details}")
            print(f"✅ 项目数量: {item_count}")
            
            # 显示完整的响应结构
            print(f"\n完整响应数据:")
            print(json.dumps(data2, ensure_ascii=False, indent=2))
            break
        else:
            print(f"响应中没有sens_details字段")
            print(f"响应数据: {json.dumps(data2, ensure_ascii=False, indent=2)}")

if __name__ == "__main__":
    test_specific_api()
