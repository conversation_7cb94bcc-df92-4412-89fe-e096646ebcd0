#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为IP地理位置CSV文件添加IP来源标识
标记公司出口IP
"""

import csv
import ipaddress
from typing import Set, List

class IPSourceMarker:
    def __init__(self):
        # 公司出口IP列表
        self.company_ips = {
            "************",
            "*************", 
            "**************",
            "**************",
            "*************",
            "**************",
            "***********",
            "**************",
            "*************",
            "************",
            "**************",
            "************",
            "*************",
            "*************",
            "**************",
            "**************",
            "************",
            "***********",
            "************",
            "************",
            "*************",
            "************",
            "************",
            "*************",
            "***********",
            "************"
        }
        
        # IP范围列表
        self.company_ip_ranges = [
            ("**************", "**************"),   # **************-75
            ("**************", "**************"),   # **************-85
            ("***************", "***************"), # ***************-109
            ("**************", "**************")    # **************-173
        ]
    
    def ip_to_int(self, ip_str: str) -> int:
        """将IP地址转换为整数"""
        try:
            return int(ipaddress.IPv4Address(ip_str))
        except:
            return 0
    
    def is_company_ip(self, ip: str) -> bool:
        """检查IP是否为公司出口IP"""
        # 检查单个IP
        if ip in self.company_ips:
            return True
        
        # 检查IP范围
        ip_int = self.ip_to_int(ip)
        if ip_int == 0:
            return False
            
        for start_ip, end_ip in self.company_ip_ranges:
            start_int = self.ip_to_int(start_ip)
            end_int = self.ip_to_int(end_ip)
            if start_int <= ip_int <= end_int:
                return True
        
        return False
    
    def process_csv(self, input_file: str = 'ip_with_location.csv', output_file: str = 'ip_with_source.csv'):
        """
        处理CSV文件，添加IP来源列
        
        Args:
            input_file: 输入CSV文件路径
            output_file: 输出CSV文件路径
        """
        try:
            with open(input_file, 'r', encoding='utf-8') as infile:
                reader = csv.DictReader(infile)
                
                # 准备输出数据
                output_data = []
                company_count = 0
                total_count = 0
                
                print("开始处理IP来源标记...")
                print("-" * 50)
                
                for row in reader:
                    ip = row.get('ip', '').strip()
                    
                    if not ip:  # 跳过空行
                        continue
                    
                    total_count += 1
                    
                    # 检查是否为公司出口IP
                    if self.is_company_ip(ip):
                        ip_source = "公司出口"
                        company_count += 1
                        print(f"✓ 公司出口IP: {ip}")
                    else:
                        ip_source = "外部访问"
                    
                    # 创建新的行数据，保持原有列的顺序并添加新列
                    output_row = row.copy()
                    output_row['IP来源'] = ip_source
                    
                    output_data.append(output_row)
                
                # 写入输出文件
                if output_data:
                    # 获取字段名，添加新的IP来源列
                    fieldnames = list(reader.fieldnames) + ['IP来源']
                    
                    with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
                        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(output_data)
                
                print("-" * 50)
                print(f"处理完成！")
                print(f"总IP数量: {total_count}")
                print(f"公司出口IP: {company_count}")
                print(f"外部访问IP: {total_count - company_count}")
                print(f"输出文件: {output_file}")
                
                # 显示公司出口IP统计
                if company_count > 0:
                    print("\n公司出口IP详情:")
                    for row in output_data:
                        if row['IP来源'] == '公司出口':
                            requests = row.get('请求次数', row.get('数量', '0'))
                            location = row.get('完整位置', '未知')
                            print(f"  {row['ip']} - 请求次数: {requests} - 位置: {location}")
                
        except FileNotFoundError:
            print(f"错误: 找不到输入文件 {input_file}")
        except Exception as e:
            print(f"处理过程中发生错误: {str(e)}")

def main():
    """主函数"""
    print("IP来源标记工具")
    print("=" * 50)
    
    marker = IPSourceMarker()
    marker.process_csv()

if __name__ == "__main__":
    main()
